-- CreateEnum
CREATE TYPE "UserRole" AS ENUM ('ADMIN', 'VENDOR', 'CUSTOMER', 'DRIVER');

-- Create<PERSON>num
CREATE TYPE "OrderStatus" AS ENUM ('PENDING', 'CONFIRMED', 'PREPARING', 'READY', 'PICKED_UP', 'ON_THE_WAY', 'DELIVERED', 'CANCELLED', 'REFUNDED');

-- CreateTable
CREATE TABLE "tap2go_users" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phoneNumber" TEXT,
    "role" "UserRole" NOT NULL DEFAULT 'CUSTOMER',
    "profileImageUrl" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isVerified" BOOLEAN NOT NULL DEFAULT false,
    "fcmTokens" TEXT[],
    "preferredLanguage" TEXT DEFAULT 'en',
    "timezone" TEXT DEFAULT 'UTC',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "lastLoginAt" TIMESTAMP(3),

    CONSTRAINT "tap2go_users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tap2go_customer_profiles" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "dateOfBirth" TIMESTAMP(3),
    "gender" TEXT,
    "loyaltyPoints" INTEGER NOT NULL DEFAULT 0,
    "totalOrders" INTEGER NOT NULL DEFAULT 0,
    "totalSpent" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "preferredCuisine" TEXT[],
    "dietaryRestrictions" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tap2go_customer_profiles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tap2go_customer_addresses" (
    "id" TEXT NOT NULL,
    "customerId" TEXT NOT NULL,
    "label" TEXT NOT NULL,
    "street" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "state" TEXT NOT NULL,
    "zipCode" TEXT NOT NULL,
    "country" TEXT NOT NULL DEFAULT 'Philippines',
    "latitude" DOUBLE PRECISION,
    "longitude" DOUBLE PRECISION,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "deliveryInstructions" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tap2go_customer_addresses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tap2go_vendor_profiles" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "businessName" TEXT NOT NULL,
    "businessType" TEXT NOT NULL,
    "businessRegistrationNumber" TEXT,
    "taxId" TEXT,
    "businessLicense" TEXT,
    "contactPersonName" TEXT NOT NULL,
    "contactPersonPhone" TEXT,
    "businessPhone" TEXT NOT NULL,
    "businessEmail" TEXT NOT NULL,
    "businessAddress" JSONB NOT NULL,
    "operatingHours" JSONB NOT NULL,
    "deliverySettings" JSONB NOT NULL,
    "isApproved" BOOLEAN NOT NULL DEFAULT false,
    "approvedAt" TIMESTAMP(3),
    "approvedBy" TEXT,
    "commissionRate" DECIMAL(5,2) NOT NULL DEFAULT 15.0,
    "totalEarnings" DECIMAL(12,2) NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tap2go_vendor_profiles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tap2go_restaurants" (
    "id" TEXT NOT NULL,
    "vendorId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "description" TEXT,
    "cuisineType" TEXT[],
    "logoUrl" TEXT,
    "bannerUrl" TEXT,
    "address" JSONB NOT NULL,
    "coordinates" JSONB,
    "phoneNumber" TEXT,
    "email" TEXT,
    "website" TEXT,
    "socialMedia" JSONB,
    "operatingHours" JSONB NOT NULL,
    "deliveryRadius" DOUBLE PRECISION NOT NULL DEFAULT 5.0,
    "minimumOrderValue" DECIMAL(8,2) NOT NULL DEFAULT 0,
    "deliveryFee" DECIMAL(6,2) NOT NULL DEFAULT 0,
    "estimatedDeliveryTime" TEXT NOT NULL DEFAULT '30-45 min',
    "acceptsScheduledOrders" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isVerified" BOOLEAN NOT NULL DEFAULT false,
    "isFeatured" BOOLEAN NOT NULL DEFAULT false,
    "rating" DOUBLE PRECISION DEFAULT 0,
    "totalReviews" INTEGER NOT NULL DEFAULT 0,
    "totalOrders" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tap2go_restaurants_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tap2go_menu_categories" (
    "id" TEXT NOT NULL,
    "restaurantId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "imageUrl" TEXT,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tap2go_menu_categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tap2go_menu_items" (
    "id" TEXT NOT NULL,
    "restaurantId" TEXT NOT NULL,
    "categoryId" TEXT,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "price" DECIMAL(8,2) NOT NULL,
    "compareAtPrice" DECIMAL(8,2),
    "imageUrl" TEXT,
    "images" TEXT[],
    "ingredients" TEXT[],
    "allergens" TEXT[],
    "nutritionInfo" JSONB,
    "preparationTime" INTEGER,
    "spicyLevel" INTEGER DEFAULT 0,
    "isVegetarian" BOOLEAN NOT NULL DEFAULT false,
    "isVegan" BOOLEAN NOT NULL DEFAULT false,
    "isGlutenFree" BOOLEAN NOT NULL DEFAULT false,
    "isAvailable" BOOLEAN NOT NULL DEFAULT true,
    "isFeatured" BOOLEAN NOT NULL DEFAULT false,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tap2go_menu_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tap2go_menu_item_customizations" (
    "id" TEXT NOT NULL,
    "menuItemId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "isRequired" BOOLEAN NOT NULL DEFAULT false,
    "options" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tap2go_menu_item_customizations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tap2go_orders" (
    "id" TEXT NOT NULL,
    "orderNumber" TEXT NOT NULL,
    "customerId" TEXT NOT NULL,
    "restaurantId" TEXT NOT NULL,
    "deliveryAddressId" TEXT,
    "driverId" TEXT,
    "status" "OrderStatus" NOT NULL DEFAULT 'PENDING',
    "orderType" TEXT NOT NULL DEFAULT 'DELIVERY',
    "paymentMethod" TEXT NOT NULL,
    "paymentStatus" TEXT NOT NULL DEFAULT 'PENDING',
    "paymentIntentId" TEXT,
    "subtotal" DECIMAL(10,2) NOT NULL,
    "deliveryFee" DECIMAL(8,2) NOT NULL DEFAULT 0,
    "serviceFee" DECIMAL(8,2) NOT NULL DEFAULT 0,
    "tax" DECIMAL(8,2) NOT NULL DEFAULT 0,
    "discount" DECIMAL(8,2) NOT NULL DEFAULT 0,
    "total" DECIMAL(10,2) NOT NULL,
    "customerNotes" TEXT,
    "restaurantNotes" TEXT,
    "driverNotes" TEXT,
    "scheduledFor" TIMESTAMP(3),
    "estimatedDeliveryTime" TIMESTAMP(3),
    "placedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "confirmedAt" TIMESTAMP(3),
    "readyAt" TIMESTAMP(3),
    "pickedUpAt" TIMESTAMP(3),
    "deliveredAt" TIMESTAMP(3),
    "cancelledAt" TIMESTAMP(3),
    "customerRating" INTEGER,
    "driverRating" INTEGER,
    "restaurantRating" INTEGER,
    "reviewSubmitted" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tap2go_orders_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tap2go_order_items" (
    "id" TEXT NOT NULL,
    "orderId" TEXT NOT NULL,
    "menuItemId" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "unitPrice" DECIMAL(8,2) NOT NULL,
    "totalPrice" DECIMAL(10,2) NOT NULL,
    "customizations" JSONB,
    "specialInstructions" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tap2go_order_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tap2go_order_tracking" (
    "id" TEXT NOT NULL,
    "orderId" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "message" TEXT,
    "location" JSONB,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "metadata" JSONB,

    CONSTRAINT "tap2go_order_tracking_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tap2go_driver_profiles" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "phoneNumber" TEXT NOT NULL,
    "dateOfBirth" TIMESTAMP(3) NOT NULL,
    "licenseNumber" TEXT NOT NULL,
    "licenseExpiryDate" TIMESTAMP(3) NOT NULL,
    "vehicleType" TEXT NOT NULL,
    "vehicleModel" TEXT,
    "vehiclePlateNumber" TEXT,
    "vehicleColor" TEXT,
    "emergencyContactName" TEXT NOT NULL,
    "emergencyContactPhone" TEXT NOT NULL,
    "bankAccountName" TEXT NOT NULL,
    "bankAccountNumber" TEXT NOT NULL,
    "bankName" TEXT NOT NULL,
    "isApproved" BOOLEAN NOT NULL DEFAULT false,
    "isOnline" BOOLEAN NOT NULL DEFAULT false,
    "isAvailable" BOOLEAN NOT NULL DEFAULT false,
    "currentLocation" JSONB,
    "deliveryRadius" DOUBLE PRECISION NOT NULL DEFAULT 10.0,
    "avgRating" DOUBLE PRECISION DEFAULT 0,
    "totalDeliveries" INTEGER NOT NULL DEFAULT 0,
    "totalEarnings" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "joinedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "approvedAt" TIMESTAMP(3),
    "approvedBy" TEXT,
    "lastActiveAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tap2go_driver_profiles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tap2go_reviews" (
    "id" TEXT NOT NULL,
    "customerId" TEXT NOT NULL,
    "restaurantId" TEXT NOT NULL,
    "orderId" TEXT,
    "rating" INTEGER NOT NULL,
    "comment" TEXT,
    "images" TEXT[],
    "isVerified" BOOLEAN NOT NULL DEFAULT false,
    "isVisible" BOOLEAN NOT NULL DEFAULT true,
    "helpfulCount" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tap2go_reviews_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tap2go_wishlists" (
    "id" TEXT NOT NULL,
    "customerId" TEXT NOT NULL,
    "restaurantId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tap2go_wishlists_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tap2go_promotions" (
    "id" TEXT NOT NULL,
    "restaurantId" TEXT,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "code" TEXT,
    "type" TEXT NOT NULL,
    "value" DECIMAL(8,2) NOT NULL,
    "minimumOrderValue" DECIMAL(8,2),
    "maxDiscountAmount" DECIMAL(8,2),
    "usageLimit" INTEGER,
    "usageCount" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tap2go_promotions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tap2go_notifications" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "data" JSONB,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tap2go_notifications_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "tap2go_users_email_key" ON "tap2go_users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "tap2go_users_phoneNumber_key" ON "tap2go_users"("phoneNumber");

-- CreateIndex
CREATE UNIQUE INDEX "tap2go_customer_profiles_userId_key" ON "tap2go_customer_profiles"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "tap2go_vendor_profiles_userId_key" ON "tap2go_vendor_profiles"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "tap2go_restaurants_slug_key" ON "tap2go_restaurants"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "tap2go_orders_orderNumber_key" ON "tap2go_orders"("orderNumber");

-- CreateIndex
CREATE UNIQUE INDEX "tap2go_driver_profiles_userId_key" ON "tap2go_driver_profiles"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "tap2go_driver_profiles_phoneNumber_key" ON "tap2go_driver_profiles"("phoneNumber");

-- CreateIndex
CREATE UNIQUE INDEX "tap2go_driver_profiles_licenseNumber_key" ON "tap2go_driver_profiles"("licenseNumber");

-- CreateIndex
CREATE UNIQUE INDEX "tap2go_wishlists_customerId_restaurantId_key" ON "tap2go_wishlists"("customerId", "restaurantId");

-- CreateIndex
CREATE UNIQUE INDEX "tap2go_promotions_code_key" ON "tap2go_promotions"("code");

-- AddForeignKey
ALTER TABLE "tap2go_customer_profiles" ADD CONSTRAINT "tap2go_customer_profiles_userId_fkey" FOREIGN KEY ("userId") REFERENCES "tap2go_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_customer_addresses" ADD CONSTRAINT "tap2go_customer_addresses_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "tap2go_customer_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_vendor_profiles" ADD CONSTRAINT "tap2go_vendor_profiles_userId_fkey" FOREIGN KEY ("userId") REFERENCES "tap2go_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_restaurants" ADD CONSTRAINT "tap2go_restaurants_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES "tap2go_vendor_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_menu_categories" ADD CONSTRAINT "tap2go_menu_categories_restaurantId_fkey" FOREIGN KEY ("restaurantId") REFERENCES "tap2go_restaurants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_menu_items" ADD CONSTRAINT "tap2go_menu_items_restaurantId_fkey" FOREIGN KEY ("restaurantId") REFERENCES "tap2go_restaurants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_menu_items" ADD CONSTRAINT "tap2go_menu_items_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "tap2go_menu_categories"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_menu_item_customizations" ADD CONSTRAINT "tap2go_menu_item_customizations_menuItemId_fkey" FOREIGN KEY ("menuItemId") REFERENCES "tap2go_menu_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_orders" ADD CONSTRAINT "tap2go_orders_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "tap2go_users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_orders" ADD CONSTRAINT "tap2go_orders_restaurantId_fkey" FOREIGN KEY ("restaurantId") REFERENCES "tap2go_restaurants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_orders" ADD CONSTRAINT "tap2go_orders_deliveryAddressId_fkey" FOREIGN KEY ("deliveryAddressId") REFERENCES "tap2go_customer_addresses"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_orders" ADD CONSTRAINT "tap2go_orders_driverId_fkey" FOREIGN KEY ("driverId") REFERENCES "tap2go_driver_profiles"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_order_items" ADD CONSTRAINT "tap2go_order_items_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "tap2go_orders"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_order_items" ADD CONSTRAINT "tap2go_order_items_menuItemId_fkey" FOREIGN KEY ("menuItemId") REFERENCES "tap2go_menu_items"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_order_tracking" ADD CONSTRAINT "tap2go_order_tracking_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "tap2go_orders"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_driver_profiles" ADD CONSTRAINT "tap2go_driver_profiles_userId_fkey" FOREIGN KEY ("userId") REFERENCES "tap2go_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_reviews" ADD CONSTRAINT "tap2go_reviews_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "tap2go_users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_reviews" ADD CONSTRAINT "tap2go_reviews_restaurantId_fkey" FOREIGN KEY ("restaurantId") REFERENCES "tap2go_restaurants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_wishlists" ADD CONSTRAINT "tap2go_wishlists_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "tap2go_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_wishlists" ADD CONSTRAINT "tap2go_wishlists_restaurantId_fkey" FOREIGN KEY ("restaurantId") REFERENCES "tap2go_restaurants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_promotions" ADD CONSTRAINT "tap2go_promotions_restaurantId_fkey" FOREIGN KEY ("restaurantId") REFERENCES "tap2go_restaurants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tap2go_notifications" ADD CONSTRAINT "tap2go_notifications_userId_fkey" FOREIGN KEY ("userId") REFERENCES "tap2go_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
