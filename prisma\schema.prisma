// Prisma Schema for Tap2Go - Hybrid Architecture
// This schema defines the core business entities for maximum scalability

generator client {
  provider = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ===== USER MANAGEMENT =====
model User {
  id                String   @id @default(cuid())
  email             String   @unique
  phoneNumber       String?  @unique
  role              UserRole @default(CUSTOMER)
  profileImageUrl   String?
  isActive          Boolean  @default(true)
  isVerified        Boolean  @default(false)
  fcmTokens         String[]
  preferredLanguage String?  @default("en")
  timezone          String?  @default("UTC")
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  lastLoginAt       DateTime?

  // Relations
  customerProfile CustomerProfile?
  vendorProfile   VendorProfile?
  driverProfile   DriverProfile?
  orders          Order[]
  reviews         Review[]
  wishlists       Wishlist[]
  notifications   Notification[]

  @@map("tap2go_users")
}

enum UserRole {
  ADMIN
  VENDOR
  CUSTOMER
  DRIVER
}

// ===== CUSTOMER PROFILES =====
model CustomerProfile {
  id              String    @id @default(cuid())
  userId          String    @unique
  firstName       String
  lastName        String
  dateOfBirth     DateTime?
  gender          String?
  loyaltyPoints   Int       @default(0)
  totalOrders     Int       @default(0)
  totalSpent      Decimal   @default(0) @db.Decimal(10, 2)
  preferredCuisine String[]
  dietaryRestrictions String[]
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  addresses       CustomerAddress[]

  @@map("tap2go_customer_profiles")
}

model CustomerAddress {
  id              String          @id @default(cuid())
  customerId      String
  label           String          // "Home", "Work", "Other"
  street          String
  city            String
  state           String
  zipCode         String
  country         String          @default("Philippines")
  latitude        Float?
  longitude       Float?
  isDefault       Boolean         @default(false)
  deliveryInstructions String?
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  // Relations
  customer        CustomerProfile @relation(fields: [customerId], references: [id], onDelete: Cascade)
  orders          Order[]

  @@map("tap2go_customer_addresses")
}

// ===== VENDOR PROFILES =====
model VendorProfile {
  id                    String    @id @default(cuid())
  userId                String    @unique
  businessName          String
  businessType          String
  businessRegistrationNumber String?
  taxId                 String?
  businessLicense       String?
  contactPersonName     String
  contactPersonPhone    String?
  businessPhone         String
  businessEmail         String
  businessAddress       Json      // Store as JSON for flexibility
  operatingHours        Json      // Store as JSON for flexibility
  deliverySettings      Json      // Store as JSON for flexibility
  isApproved            Boolean   @default(false)
  approvedAt            DateTime?
  approvedBy            String?
  commissionRate        Decimal   @default(15.0) @db.Decimal(5, 2)
  totalEarnings         Decimal   @default(0) @db.Decimal(12, 2)
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  // Relations
  user                  User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  restaurants           Restaurant[]

  @@map("tap2go_vendor_profiles")
}

// ===== RESTAURANTS =====
model Restaurant {
  id                  String        @id @default(cuid())
  vendorId            String
  name                String
  slug                String        @unique
  description         String?
  cuisineType         String[]
  logoUrl             String?
  bannerUrl           String?
  address             Json          // Store as JSON for flexibility
  coordinates         Json?         // Store lat/lng as JSON
  phoneNumber         String?
  email               String?
  website             String?
  socialMedia         Json?         // Store social links as JSON
  operatingHours      Json          // Store hours as JSON
  deliveryRadius      Float         @default(5.0)
  minimumOrderValue   Decimal       @default(0) @db.Decimal(8, 2)
  deliveryFee         Decimal       @default(0) @db.Decimal(6, 2)
  estimatedDeliveryTime String      @default("30-45 min")
  acceptsScheduledOrders Boolean    @default(false)
  isActive            Boolean       @default(true)
  isVerified          Boolean       @default(false)
  isFeatured          Boolean       @default(false)
  rating              Float?        @default(0)
  totalReviews        Int           @default(0)
  totalOrders         Int           @default(0)
  createdAt           DateTime      @default(now())
  updatedAt           DateTime      @updatedAt

  // Relations
  vendor              VendorProfile @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  menuCategories      MenuCategory[]
  menuItems           MenuItem[]
  orders              Order[]
  reviews             Review[]
  wishlists           Wishlist[]
  promotions          Promotion[]

  @@map("tap2go_restaurants")
}

// ===== MENU MANAGEMENT =====
model MenuCategory {
  id            String     @id @default(cuid())
  restaurantId  String
  name          String
  description   String?
  imageUrl      String?
  sortOrder     Int        @default(0)
  isActive      Boolean    @default(true)
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  // Relations
  restaurant    Restaurant @relation(fields: [restaurantId], references: [id], onDelete: Cascade)
  menuItems     MenuItem[]

  @@map("tap2go_menu_categories")
}

model MenuItem {
  id              String        @id @default(cuid())
  restaurantId    String
  categoryId      String?
  name            String
  description     String?
  price           Decimal       @db.Decimal(8, 2)
  compareAtPrice  Decimal?      @db.Decimal(8, 2)
  imageUrl        String?
  images          String[]      // Multiple images
  ingredients     String[]      // List of ingredients
  allergens       String[]      // List of allergens
  nutritionInfo   Json?         // Store nutrition as JSON
  preparationTime Int?          // In minutes
  spicyLevel      Int?          @default(0) // 0-5 scale
  isVegetarian    Boolean       @default(false)
  isVegan         Boolean       @default(false)
  isGlutenFree    Boolean       @default(false)
  isAvailable     Boolean       @default(true)
  isFeatured      Boolean       @default(false)
  sortOrder       Int           @default(0)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  restaurant      Restaurant    @relation(fields: [restaurantId], references: [id], onDelete: Cascade)
  category        MenuCategory? @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  orderItems      OrderItem[]
  customizations  MenuItemCustomization[]

  @@map("tap2go_menu_items")
}

model MenuItemCustomization {
  id          String   @id @default(cuid())
  menuItemId  String
  name        String   // "Size", "Spice Level", "Add-ons"
  type        String   // "SINGLE_SELECT", "MULTI_SELECT", "TEXT_INPUT"
  isRequired  Boolean  @default(false)
  options     Json     // Store options as JSON array
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  menuItem    MenuItem @relation(fields: [menuItemId], references: [id], onDelete: Cascade)

  @@map("tap2go_menu_item_customizations")
}

// ===== ORDER MANAGEMENT =====
model Order {
  id                    String      @id @default(cuid())
  orderNumber           String      @unique
  customerId            String
  restaurantId          String
  deliveryAddressId     String?
  driverId              String?
  status                OrderStatus @default(PENDING)
  orderType             String      @default("DELIVERY") // DELIVERY, PICKUP
  paymentMethod         String
  paymentStatus         String      @default("PENDING")
  paymentIntentId       String?     // PayMongo payment intent ID

  // Pricing
  subtotal              Decimal     @db.Decimal(10, 2)
  deliveryFee           Decimal     @default(0) @db.Decimal(8, 2)
  serviceFee            Decimal     @default(0) @db.Decimal(8, 2)
  tax                   Decimal     @default(0) @db.Decimal(8, 2)
  discount              Decimal     @default(0) @db.Decimal(8, 2)
  total                 Decimal     @db.Decimal(10, 2)

  // Special Instructions
  customerNotes         String?
  restaurantNotes       String?
  driverNotes           String?

  // Scheduling
  scheduledFor          DateTime?
  estimatedDeliveryTime DateTime?

  // Timestamps
  placedAt              DateTime    @default(now())
  confirmedAt           DateTime?
  readyAt               DateTime?
  pickedUpAt            DateTime?
  deliveredAt           DateTime?
  cancelledAt           DateTime?

  // Ratings
  customerRating        Int?        // 1-5 stars
  driverRating          Int?        // 1-5 stars
  restaurantRating      Int?        // 1-5 stars
  reviewSubmitted       Boolean     @default(false)

  createdAt             DateTime    @default(now())
  updatedAt             DateTime    @updatedAt

  // Relations
  customer              User            @relation(fields: [customerId], references: [id])
  restaurant            Restaurant      @relation(fields: [restaurantId], references: [id])
  deliveryAddress       CustomerAddress? @relation(fields: [deliveryAddressId], references: [id])
  driver                DriverProfile?   @relation(fields: [driverId], references: [id])
  orderItems            OrderItem[]
  orderTracking         OrderTracking[]

  @@map("tap2go_orders")
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PREPARING
  READY
  PICKED_UP
  ON_THE_WAY
  DELIVERED
  CANCELLED
  REFUNDED
}

model OrderItem {
  id              String   @id @default(cuid())
  orderId         String
  menuItemId      String
  quantity        Int
  unitPrice       Decimal  @db.Decimal(8, 2)
  totalPrice      Decimal  @db.Decimal(10, 2)
  customizations  Json?    // Store customizations as JSON
  specialInstructions String?
  createdAt       DateTime @default(now())

  // Relations
  order           Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  menuItem        MenuItem @relation(fields: [menuItemId], references: [id])

  @@map("tap2go_order_items")
}

model OrderTracking {
  id          String   @id @default(cuid())
  orderId     String
  status      String
  message     String?
  location    Json?    // Store lat/lng as JSON
  timestamp   DateTime @default(now())
  metadata    Json?    // Additional tracking data

  // Relations
  order       Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("tap2go_order_tracking")
}

// ===== DRIVER MANAGEMENT =====
model DriverProfile {
  id                  String    @id @default(cuid())
  userId              String    @unique
  firstName           String
  lastName            String
  phoneNumber         String    @unique
  dateOfBirth         DateTime
  licenseNumber       String    @unique
  licenseExpiryDate   DateTime
  vehicleType         String    // "MOTORCYCLE", "CAR", "BICYCLE"
  vehicleModel        String?
  vehiclePlateNumber  String?
  vehicleColor        String?
  emergencyContactName String
  emergencyContactPhone String
  bankAccountName     String
  bankAccountNumber   String
  bankName            String
  isApproved          Boolean   @default(false)
  isOnline            Boolean   @default(false)
  isAvailable         Boolean   @default(false)
  currentLocation     Json?     // Store lat/lng as JSON
  deliveryRadius      Float     @default(10.0)
  avgRating           Float?    @default(0)
  totalDeliveries     Int       @default(0)
  totalEarnings       Decimal   @default(0) @db.Decimal(10, 2)
  joinedAt            DateTime  @default(now())
  approvedAt          DateTime?
  approvedBy          String?
  lastActiveAt        DateTime?
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt

  // Relations
  user                User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  orders              Order[]

  @@map("tap2go_driver_profiles")
}

// ===== REVIEWS & RATINGS =====
model Review {
  id            String     @id @default(cuid())
  customerId    String
  restaurantId  String
  orderId       String?
  rating        Int        // 1-5 stars
  comment       String?
  images        String[]   // Review images
  isVerified    Boolean    @default(false)
  isVisible     Boolean    @default(true)
  helpfulCount  Int        @default(0)
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  // Relations
  customer      User       @relation(fields: [customerId], references: [id])
  restaurant    Restaurant @relation(fields: [restaurantId], references: [id])

  @@map("tap2go_reviews")
}

// ===== WISHLISTS =====
model Wishlist {
  id            String     @id @default(cuid())
  customerId    String
  restaurantId  String
  createdAt     DateTime   @default(now())

  // Relations
  customer      User       @relation(fields: [customerId], references: [id], onDelete: Cascade)
  restaurant    Restaurant @relation(fields: [restaurantId], references: [id], onDelete: Cascade)

  @@unique([customerId, restaurantId])
  @@map("tap2go_wishlists")
}

// ===== PROMOTIONS =====
model Promotion {
  id              String     @id @default(cuid())
  restaurantId    String?
  title           String
  description     String?
  code            String?    @unique
  type            String     // "PERCENTAGE", "FIXED_AMOUNT", "FREE_DELIVERY"
  value           Decimal    @db.Decimal(8, 2)
  minimumOrderValue Decimal? @db.Decimal(8, 2)
  maxDiscountAmount Decimal? @db.Decimal(8, 2)
  usageLimit      Int?
  usageCount      Int        @default(0)
  isActive        Boolean    @default(true)
  startDate       DateTime
  endDate         DateTime
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // Relations
  restaurant      Restaurant? @relation(fields: [restaurantId], references: [id], onDelete: Cascade)

  @@map("tap2go_promotions")
}

// ===== NOTIFICATIONS =====
model Notification {
  id          String   @id @default(cuid())
  userId      String
  title       String
  message     String
  type        String   // "ORDER_UPDATE", "PROMOTION", "SYSTEM"
  data        Json?    // Additional notification data
  isRead      Boolean  @default(false)
  createdAt   DateTime @default(now())

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("tap2go_notifications")
}
