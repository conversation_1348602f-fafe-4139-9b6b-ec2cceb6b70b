// Prisma Schema for Tap2Go - CMS Content Only
// This schema defines ONLY CMS content tables
// Business logic tables are handled by Firestore

generator client {
  provider = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ===== BLOG CONTENT MODEL =====
// This model represents blog content stored in Neon PostgreSQL
// All business logic models are handled by Firestore

// Blog posts for content marketing and SEO
model BlogPost {
  id              Int       @id @default(autoincrement())
  title           String
  slug            String    @unique
  content         String
  excerpt         String?
  featuredImageUrl String?  @map("featured_image_url")
  authorName      String?   @map("author_name")
  authorBio       String?   @map("author_bio")
  authorAvatarUrl String?   @map("author_avatar_url")
  categories      Json      @default("[]")
  tags            Json      @default("[]")
  relatedRestaurants Json   @default("[]") @map("related_restaurants")
  readingTime     Int?      @map("reading_time")
  isPublished     Boolean   @default(false) @map("is_published")
  isFeatured      Boolean   @default(false) @map("is_featured")
  seoData         Json      @default("{}") @map("seo_data")
  publishedAt     DateTime? @map("published_at")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("blog_posts")
}

// ===== END OF BLOG SCHEMA =====
// All business logic models (Users, Orders, Restaurants, etc.) are handled by Firestore
// This Prisma schema only contains the blog post model for PostgreSQL
