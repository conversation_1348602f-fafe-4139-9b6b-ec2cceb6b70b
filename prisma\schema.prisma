// Prisma Schema for Tap2Go - CMS Content Only
// This schema defines ONLY CMS content tables
// Business logic tables are handled by Firestore

generator client {
  provider = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ===== CMS CONTENT MODELS =====
// These models represent content stored in Neon PostgreSQL
// Business logic models are handled by Firestore

// Restaurant extended content (linked to Firestore restaurants via firebase_id)
model RestaurantContent {
  id              Int       @id @default(autoincrement())
  firebaseId      String    @unique @map("firebase_id")
  slug            String    @unique
  story           String?
  longDescription String?   @map("long_description")
  heroImageUrl    String?   @map("hero_image_url")
  galleryImages   Json      @default("[]") @map("gallery_images")
  awards          Json      @default("[]")
  certifications  Json      @default("[]")
  specialFeatures Json      @default("[]") @map("special_features")
  socialMedia     Json      @default("{}") @map("social_media")
  seoData         Json      @default("{}") @map("seo_data")
  isPublished     Boolean   @default(false) @map("is_published")
  publishedAt     DateTime? @map("published_at")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("restaurant_contents")
}

// Menu item extended content (linked to Firestore menu items via firebase_id)
model MenuItemContent {
  id                    Int       @id @default(autoincrement())
  firebaseId            String    @unique @map("firebase_id")
  restaurantFirebaseId  String    @map("restaurant_firebase_id")
  detailedDescription   String?   @map("detailed_description")
  preparationStory      String?   @map("preparation_story")
  chefNotes             String?   @map("chef_notes")
  ingredientStory       String?   @map("ingredient_story")
  images                Json      @default("[]")
  nutritionalDetails    Json      @default("{}") @map("nutritional_details")
  preparationSteps      Json      @default("[]") @map("preparation_steps")
  pairingSuggestions    String?   @map("pairing_suggestions")
  allergenDetails       Json      @default("{}") @map("allergen_details")
  sustainabilityInfo    String?   @map("sustainability_info")
  originStory           String?   @map("origin_story")
  seasonalAvailability  String?   @map("seasonal_availability")
  seoData               Json      @default("{}") @map("seo_data")
  isPublished           Boolean   @default(false) @map("is_published")
  publishedAt           DateTime? @map("published_at")
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("menu_item_contents")
}

// Blog posts for content marketing
model BlogPost {
  id              Int       @id @default(autoincrement())
  title           String
  slug            String    @unique
  content         String
  excerpt         String?
  featuredImageUrl String?  @map("featured_image_url")
  authorName      String?   @map("author_name")
  authorBio       String?   @map("author_bio")
  authorAvatarUrl String?   @map("author_avatar_url")
  categories      Json      @default("[]")
  tags            Json      @default("[]")
  relatedRestaurants Json   @default("[]") @map("related_restaurants")
  readingTime     Int?      @map("reading_time")
  isPublished     Boolean   @default(false) @map("is_published")
  isFeatured      Boolean   @default(false) @map("is_featured")
  seoData         Json      @default("{}") @map("seo_data")
  publishedAt     DateTime? @map("published_at")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("blog_posts")
}

// Promotion content for marketing campaigns
model PromotionContent {
  id                      Int       @id @default(autoincrement())
  firebaseId              String?   @unique @map("firebase_id")
  title                   String
  slug                    String    @unique
  description             String?
  longDescription         String?   @map("long_description")
  termsAndConditions      String?   @map("terms_and_conditions")
  bannerImageUrl          String?   @map("banner_image_url")
  galleryImages           Json      @default("[]") @map("gallery_images")
  targetAudience          Json      @default("{}") @map("target_audience")
  marketingCopy           String?   @map("marketing_copy")
  socialMediaContent      Json      @default("{}") @map("social_media_content")
  emailContent            String?   @map("email_content")
  smsContent              String?   @map("sms_content")
  pushNotificationContent String?   @map("push_notification_content")
  seoData                 Json      @default("{}") @map("seo_data")
  isPublished             Boolean   @default(false) @map("is_published")
  publishedAt             DateTime? @map("published_at")
  createdAt               DateTime  @default(now()) @map("created_at")
  updatedAt               DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("promotion_contents")
}

// Static pages for website content
model StaticPage {
  id                Int       @id @default(autoincrement())
  title             String
  slug              String    @unique
  content           String
  excerpt           String?
  pageType          String    @default("general") @map("page_type")
  metaTitle         String?   @map("meta_title")
  metaDescription   String?   @map("meta_description")
  featuredImageUrl  String?   @map("featured_image_url")
  isPublished       Boolean   @default(false) @map("is_published")
  showInNavigation  Boolean   @default(false) @map("show_in_navigation")
  navigationOrder   Int?      @map("navigation_order")
  seoData           Json      @default("{}") @map("seo_data")
  publishedAt       DateTime? @map("published_at")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("static_pages")
}

// ===== END OF CMS SCHEMA =====
// All business logic models (Users, Orders, Restaurants, etc.) are handled by Firestore
// This Prisma schema only contains CMS content models for PostgreSQL
