/**
 * Simple Neon Database Cleanup Script
 * Removes all tables and creates CMS-only schema
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { neon } = require('@neondatabase/serverless');
const readline = require('readline');

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Promisify readline question
const prompt = (question) => {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
};

/**
 * Main cleanup function
 */
async function cleanNeonDatabase() {
  try {
    console.log('🧹 Tap2Go Neon Database Cleanup\n');
    
    // Check environment variables
    if (!process.env.DATABASE_URL) {
      console.error('❌ DATABASE_URL environment variable not set');
      console.log('Please add your Neon database URL to .env.local');
      process.exit(1);
    }
    
    // Initialize Neon client
    const sql = neon(process.env.DATABASE_URL);
    
    // Test connection
    console.log('🔌 Testing database connection...');
    try {
      const result = await sql`SELECT NOW() as current_time`;
      console.log('✅ Database connection successful!');
    } catch (error) {
      console.error('❌ Cannot connect to database:', error.message);
      process.exit(1);
    }
    
    // Show current tables
    console.log('\n📊 Current Database Tables:');
    try {
      const tables = await sql`
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY tablename
      `;
      
      if (tables.length === 0) {
        console.log('   No tables found (clean database)');
      } else {
        console.log(`   Found ${tables.length} tables:`);
        tables.forEach(table => {
          console.log(`   ✓ ${table.tablename}`);
        });
      }
    } catch (error) {
      console.log('   Could not retrieve table list');
    }
    
    // Confirm cleanup
    console.log('\n⚠️  WARNING: This will delete ALL existing tables!');
    console.log('This includes:');
    console.log('  - All Prisma business logic tables (tap2go_*)');
    console.log('  - All existing CMS tables');
    console.log('  - All custom types and enums');
    console.log('\nFirestore data will remain untouched.');
    
    const confirm = await prompt('\nAre you sure you want to proceed? (yes/no): ');
    
    if (confirm.toLowerCase() !== 'yes') {
      console.log('Cleanup cancelled.');
      rl.close();
      return;
    }
    
    // Drop all tables
    console.log('\n🗑️  Dropping all tables...');
    try {
      // Get all table names
      const tables = await sql`
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
      `;
      
      // Drop each table individually to avoid issues
      for (const table of tables) {
        const tableName = table.tablename;
        await sql.query(`DROP TABLE IF EXISTS "${tableName}" CASCADE`);
        console.log(`   Dropped: ${tableName}`);
      }
      
      // Drop custom types
      await sql.query(`DROP TYPE IF EXISTS "UserRole" CASCADE`);
      await sql.query(`DROP TYPE IF EXISTS "OrderStatus" CASCADE`);
      
      console.log('✅ All tables and types dropped successfully!');
      
    } catch (error) {
      console.error('❌ Error dropping tables:', error.message);
      throw error;
    }
    
    // Create blog-only schema
    console.log('\n🏗️  Creating blog-only schema...');

    try {
      // Blog posts table - ONLY table we need
      await sql`
        CREATE TABLE blog_posts (
          id SERIAL PRIMARY KEY,
          title VARCHAR(255) NOT NULL,
          slug VARCHAR(255) UNIQUE NOT NULL,
          content TEXT NOT NULL,
          excerpt TEXT,
          featured_image_url TEXT,
          author_name VARCHAR(255),
          author_bio TEXT,
          author_avatar_url TEXT,
          categories JSONB DEFAULT '[]',
          tags JSONB DEFAULT '[]',
          related_restaurants JSONB DEFAULT '[]',
          reading_time INTEGER,
          is_published BOOLEAN DEFAULT false,
          is_featured BOOLEAN DEFAULT false,
          seo_data JSONB DEFAULT '{}',
          published_at TIMESTAMP,
          created_at TIMESTAMP DEFAULT NOW(),
          updated_at TIMESTAMP DEFAULT NOW()
        )
      `;

      console.log('✅ Blog-only schema created successfully!');
      
    } catch (error) {
      console.error('❌ Error creating CMS schema:', error.message);
      throw error;
    }
    
    // Create indexes for blog posts only
    console.log('\n📈 Creating performance indexes...');
    try {
      await sql.query(`CREATE INDEX idx_blog_posts_slug ON blog_posts(slug)`);
      await sql.query(`CREATE INDEX idx_blog_posts_published ON blog_posts(is_published)`);
      await sql.query(`CREATE INDEX idx_blog_posts_featured ON blog_posts(is_featured)`);
      await sql.query(`CREATE INDEX idx_blog_posts_categories ON blog_posts USING GIN(categories)`);
      await sql.query(`CREATE INDEX idx_blog_posts_tags ON blog_posts USING GIN(tags)`);

      console.log('✅ Blog performance indexes created successfully!');
    } catch (error) {
      console.log('⚠️  Some indexes may already exist, continuing...');
    }
    
    // Show final state
    console.log('\n📊 Final Database State:');
    const finalTables = await sql`
      SELECT tablename 
      FROM pg_tables 
      WHERE schemaname = 'public'
      ORDER BY tablename
    `;
    
    finalTables.forEach(table => {
      console.log(`   ✓ ${table.tablename}`);
    });
    
    console.log('\n✅ Database cleanup completed successfully!');
    console.log('\n📋 What was done:');
    console.log('  ✓ Removed all business logic tables');
    console.log('  ✓ Removed Prisma migration tables');
    console.log('  ✓ Created clean blog-only schema');
    console.log('  ✓ Added blog performance indexes');

    console.log('\n🎯 Architecture Summary:');
    console.log('  🔥 Firestore: All business logic (users, orders, restaurants, etc.)');
    console.log('  🗄️  Neon PostgreSQL: Blog posts only');

    console.log('\n📋 Next Steps:');
    console.log('1. Update Prisma schema to include only blog posts');
    console.log('2. Test blog integration with clean database');
    console.log('3. Create blog post management interface');
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n👋 Cleanup interrupted');
  rl.close();
  process.exit(0);
});

// Run the cleanup
cleanNeonDatabase();
