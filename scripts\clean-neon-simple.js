/**
 * Simple Neon Database Cleanup Script
 * Removes all tables and creates CMS-only schema
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { neon } = require('@neondatabase/serverless');
const readline = require('readline');

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Promisify readline question
const prompt = (question) => {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
};

/**
 * Main cleanup function
 */
async function cleanNeonDatabase() {
  try {
    console.log('🧹 Tap2Go Neon Database Cleanup\n');
    
    // Check environment variables
    if (!process.env.DATABASE_URL) {
      console.error('❌ DATABASE_URL environment variable not set');
      console.log('Please add your Neon database URL to .env.local');
      process.exit(1);
    }
    
    // Initialize Neon client
    const sql = neon(process.env.DATABASE_URL);
    
    // Test connection
    console.log('🔌 Testing database connection...');
    try {
      const result = await sql`SELECT NOW() as current_time`;
      console.log('✅ Database connection successful!');
    } catch (error) {
      console.error('❌ Cannot connect to database:', error.message);
      process.exit(1);
    }
    
    // Show current tables
    console.log('\n📊 Current Database Tables:');
    try {
      const tables = await sql`
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY tablename
      `;
      
      if (tables.length === 0) {
        console.log('   No tables found (clean database)');
      } else {
        console.log(`   Found ${tables.length} tables:`);
        tables.forEach(table => {
          console.log(`   ✓ ${table.tablename}`);
        });
      }
    } catch (error) {
      console.log('   Could not retrieve table list');
    }
    
    // Confirm cleanup
    console.log('\n⚠️  WARNING: This will delete ALL existing tables!');
    console.log('This includes:');
    console.log('  - All Prisma business logic tables (tap2go_*)');
    console.log('  - All existing CMS tables');
    console.log('  - All custom types and enums');
    console.log('\nFirestore data will remain untouched.');
    
    const confirm = await prompt('\nAre you sure you want to proceed? (yes/no): ');
    
    if (confirm.toLowerCase() !== 'yes') {
      console.log('Cleanup cancelled.');
      rl.close();
      return;
    }
    
    // Drop all tables
    console.log('\n🗑️  Dropping all tables...');
    try {
      // Get all table names
      const tables = await sql`
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
      `;
      
      // Drop each table individually to avoid issues
      for (const table of tables) {
        const tableName = table.tablename;
        await sql.query(`DROP TABLE IF EXISTS "${tableName}" CASCADE`);
        console.log(`   Dropped: ${tableName}`);
      }
      
      // Drop custom types
      await sql.query(`DROP TYPE IF EXISTS "UserRole" CASCADE`);
      await sql.query(`DROP TYPE IF EXISTS "OrderStatus" CASCADE`);
      
      console.log('✅ All tables and types dropped successfully!');
      
    } catch (error) {
      console.error('❌ Error dropping tables:', error.message);
      throw error;
    }
    
    // Create CMS-only schema
    console.log('\n🏗️  Creating CMS-only schema...');
    
    try {
      // Restaurant content table
      await sql`
        CREATE TABLE restaurant_contents (
          id SERIAL PRIMARY KEY,
          firebase_id VARCHAR(255) UNIQUE NOT NULL,
          slug VARCHAR(255) UNIQUE NOT NULL,
          story TEXT,
          long_description TEXT,
          hero_image_url TEXT,
          gallery_images JSONB DEFAULT '[]',
          awards JSONB DEFAULT '[]',
          certifications JSONB DEFAULT '[]',
          special_features JSONB DEFAULT '[]',
          social_media JSONB DEFAULT '{}',
          seo_data JSONB DEFAULT '{}',
          is_published BOOLEAN DEFAULT false,
          published_at TIMESTAMP,
          created_at TIMESTAMP DEFAULT NOW(),
          updated_at TIMESTAMP DEFAULT NOW()
        )
      `;
      
      // Menu item contents table
      await sql`
        CREATE TABLE menu_item_contents (
          id SERIAL PRIMARY KEY,
          firebase_id VARCHAR(255) UNIQUE NOT NULL,
          restaurant_firebase_id VARCHAR(255) NOT NULL,
          detailed_description TEXT,
          preparation_story TEXT,
          chef_notes TEXT,
          ingredient_story TEXT,
          images JSONB DEFAULT '[]',
          nutritional_details JSONB DEFAULT '{}',
          preparation_steps JSONB DEFAULT '[]',
          pairing_suggestions TEXT,
          allergen_details JSONB DEFAULT '{}',
          sustainability_info TEXT,
          origin_story TEXT,
          seasonal_availability TEXT,
          seo_data JSONB DEFAULT '{}',
          is_published BOOLEAN DEFAULT false,
          published_at TIMESTAMP,
          created_at TIMESTAMP DEFAULT NOW(),
          updated_at TIMESTAMP DEFAULT NOW()
        )
      `;
      
      // Blog posts table
      await sql`
        CREATE TABLE blog_posts (
          id SERIAL PRIMARY KEY,
          title VARCHAR(255) NOT NULL,
          slug VARCHAR(255) UNIQUE NOT NULL,
          content TEXT NOT NULL,
          excerpt TEXT,
          featured_image_url TEXT,
          author_name VARCHAR(255),
          author_bio TEXT,
          author_avatar_url TEXT,
          categories JSONB DEFAULT '[]',
          tags JSONB DEFAULT '[]',
          related_restaurants JSONB DEFAULT '[]',
          reading_time INTEGER,
          is_published BOOLEAN DEFAULT false,
          is_featured BOOLEAN DEFAULT false,
          seo_data JSONB DEFAULT '{}',
          published_at TIMESTAMP,
          created_at TIMESTAMP DEFAULT NOW(),
          updated_at TIMESTAMP DEFAULT NOW()
        )
      `;
      
      // Promotion contents table
      await sql`
        CREATE TABLE promotion_contents (
          id SERIAL PRIMARY KEY,
          firebase_id VARCHAR(255) UNIQUE,
          title VARCHAR(255) NOT NULL,
          slug VARCHAR(255) UNIQUE NOT NULL,
          description TEXT,
          long_description TEXT,
          terms_and_conditions TEXT,
          banner_image_url TEXT,
          gallery_images JSONB DEFAULT '[]',
          target_audience JSONB DEFAULT '{}',
          marketing_copy TEXT,
          social_media_content JSONB DEFAULT '{}',
          email_content TEXT,
          sms_content TEXT,
          push_notification_content TEXT,
          seo_data JSONB DEFAULT '{}',
          is_published BOOLEAN DEFAULT false,
          published_at TIMESTAMP,
          created_at TIMESTAMP DEFAULT NOW(),
          updated_at TIMESTAMP DEFAULT NOW()
        )
      `;
      
      // Static pages table
      await sql`
        CREATE TABLE static_pages (
          id SERIAL PRIMARY KEY,
          title VARCHAR(255) NOT NULL,
          slug VARCHAR(255) UNIQUE NOT NULL,
          content TEXT NOT NULL,
          excerpt TEXT,
          page_type VARCHAR(100) DEFAULT 'general',
          meta_title VARCHAR(255),
          meta_description TEXT,
          featured_image_url TEXT,
          is_published BOOLEAN DEFAULT false,
          show_in_navigation BOOLEAN DEFAULT false,
          navigation_order INTEGER,
          seo_data JSONB DEFAULT '{}',
          published_at TIMESTAMP,
          created_at TIMESTAMP DEFAULT NOW(),
          updated_at TIMESTAMP DEFAULT NOW()
        )
      `;
      
      console.log('✅ CMS-only schema created successfully!');
      
    } catch (error) {
      console.error('❌ Error creating CMS schema:', error.message);
      throw error;
    }
    
    // Create indexes
    console.log('\n📈 Creating performance indexes...');
    try {
      await sql.query(`CREATE INDEX idx_restaurant_contents_firebase_id ON restaurant_contents(firebase_id)`);
      await sql.query(`CREATE INDEX idx_restaurant_contents_slug ON restaurant_contents(slug)`);
      await sql.query(`CREATE INDEX idx_restaurant_contents_published ON restaurant_contents(is_published)`);
      await sql.query(`CREATE INDEX idx_menu_item_contents_firebase_id ON menu_item_contents(firebase_id)`);
      await sql.query(`CREATE INDEX idx_menu_item_contents_restaurant ON menu_item_contents(restaurant_firebase_id)`);
      await sql.query(`CREATE INDEX idx_blog_posts_slug ON blog_posts(slug)`);
      await sql.query(`CREATE INDEX idx_blog_posts_published ON blog_posts(is_published)`);
      await sql.query(`CREATE INDEX idx_blog_posts_featured ON blog_posts(is_featured)`);
      await sql.query(`CREATE INDEX idx_promotion_contents_slug ON promotion_contents(slug)`);
      await sql.query(`CREATE INDEX idx_promotion_contents_published ON promotion_contents(is_published)`);
      await sql.query(`CREATE INDEX idx_static_pages_slug ON static_pages(slug)`);
      await sql.query(`CREATE INDEX idx_static_pages_published ON static_pages(is_published)`);

      console.log('✅ Performance indexes created successfully!');
    } catch (error) {
      console.log('⚠️  Some indexes may already exist, continuing...');
    }
    
    // Show final state
    console.log('\n📊 Final Database State:');
    const finalTables = await sql`
      SELECT tablename 
      FROM pg_tables 
      WHERE schemaname = 'public'
      ORDER BY tablename
    `;
    
    finalTables.forEach(table => {
      console.log(`   ✓ ${table.tablename}`);
    });
    
    console.log('\n✅ Database cleanup completed successfully!');
    console.log('\n📋 What was done:');
    console.log('  ✓ Removed all business logic tables');
    console.log('  ✓ Removed Prisma migration tables');
    console.log('  ✓ Created clean CMS-only schema');
    console.log('  ✓ Added performance indexes');
    
    console.log('\n🎯 Architecture Summary:');
    console.log('  🔥 Firestore: All business logic (users, orders, restaurants, etc.)');
    console.log('  🗄️  Neon PostgreSQL: CMS content only (blog posts, extended content)');
    
    console.log('\n📋 Next Steps:');
    console.log('1. Update Prisma schema to remove business tables');
    console.log('2. Test CMS integration with clean database');
    console.log('3. Verify Firestore continues to handle all business operations');
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n👋 Cleanup interrupted');
  rl.close();
  process.exit(0);
});

// Run the cleanup
cleanNeonDatabase();
